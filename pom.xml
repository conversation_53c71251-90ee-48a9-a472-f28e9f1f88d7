<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.deepinnet</groupId>
    <artifactId>sail</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>sail-api-model</module>
        <module>sail-service</module>
        <module>sail-web</module>
        <module>sail-dal</module>
        <module>sail-app-starter</module>
        <module>sail-common</module>
    </modules>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring-boot.version>2.6.13</spring-boot.version>
        <jasypt.version>3.0.5</jasypt.version>
        <pageHelper.version>1.4.7</pageHelper.version>
        <mapstruct.version>1.5.0.Final</mapstruct.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <lombok.version>1.18.24</lombok.version>
        <digital-boot-starter.version>1.0.0.2025.07.23-SNAPSHOT</digital-boot-starter.version>
        <infra-api-model.version>20250813.1-SNAPSHOT</infra-api-model.version>
        <feigin.version>3.1.9</feigin.version>
        <satoken.version>1.38.0</satoken.version>
        <spring-redis-starter.version>2.6.13</spring-redis-starter.version>
        <jts.version>1.19.0</jts.version>
        <postgis.version>2.5.0</postgis.version>
        <logback.version>6.6</logback.version>
        <commons-pool2.version>2.12.1</commons-pool2.version>
        <hutool.version>5.8.9</hutool.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>

        <springfox.version>3.0.0</springfox.version>
        <knife4j.version>4.4.0</knife4j.version>

        <spatiotemporalplatform-api-model.version>20250813.0-SNAPSHOT</spatiotemporalplatform-api-model.version>
        <sail-api-model.version>1.0-SNAPSHOT</sail-api-model.version>
        <digitaltwincommon.version>1.3-SNAPSHOT</digitaltwincommon.version>
        <local-data-integration.version>1.1.20250814-SNAPSHOT</local-data-integration.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>${lombok-mapstruct-binding.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>

        <dependency>
            <groupId>com.deepinnet</groupId>
            <artifactId>digitaltwincommon</artifactId>
            <version>${digitaltwincommon.version}</version>
        </dependency>



    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>digital-boot-starter</artifactId>
                <version>${digital-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>spatiotemporalplatform-infrastructure-api-model</artifactId>
                <version>${infra-api-model.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${feigin.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring-redis-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- 连接池 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-core</artifactId>
                <version>${springfox.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>sail-app-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>sail-dal</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>sail-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>sail-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>sail-api-model</artifactId>
                <version>${sail-api-model.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pageHelper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>


            <dependency>
                <groupId>org.locationtech.jts</groupId>
                <artifactId>jts-core</artifactId>
                <version>${jts.version}</version>
            </dependency>

            <dependency>
                <groupId>net.postgis</groupId>
                <artifactId>postgis-jdbc</artifactId>
                <version>${postgis.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.19</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>spatiotemporalplatform-api-model</artifactId>
                <version>${spatiotemporalplatform-api-model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.deepinnet</groupId>
                        <artifactId>local-data-integration</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>local-data-integration</artifactId>
                <version>${local-data-integration.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>sail</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>