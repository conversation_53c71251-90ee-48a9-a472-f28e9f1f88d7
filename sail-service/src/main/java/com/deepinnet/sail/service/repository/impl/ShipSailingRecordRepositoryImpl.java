package com.deepinnet.sail.service.repository.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.condition.SailingRecordQueryCondition;
import com.deepinnet.sail.dal.dataobject.ShipSailingRecordDO;
import com.deepinnet.sail.dal.dto.DailySailingCountDTO;
import com.deepinnet.sail.dal.dto.PortAreaSailingRankDTO;
import com.deepinnet.sail.dal.mapper.ShipSailingRecordMapper;
import com.deepinnet.sail.service.dto.SailingRecordQueryDTO;
import com.deepinnet.sail.service.enums.SailingReturnStatusEnum;
import com.deepinnet.sail.service.repository.ShipSailingRecordRepository;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 船舶出海记录表 Repository 实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@Service
public class ShipSailingRecordRepositoryImpl extends ServiceImpl<ShipSailingRecordMapper, ShipSailingRecordDO>
        implements ShipSailingRecordRepository {

    @Override
    public ShipSailingRecordDO findCurrentSailingRecord(String shipNo) {
        if (StrUtil.isBlank(shipNo)) {
            return null;
        }

        LambdaQueryWrapper<ShipSailingRecordDO> wrapper = Wrappers.lambdaQuery(ShipSailingRecordDO.class)
                .eq(ShipSailingRecordDO::getShipNo, shipNo)
                .eq(ShipSailingRecordDO::getReturnStatus, SailingReturnStatusEnum.SAILING.getCode())
                .orderByDesc(ShipSailingRecordDO::getDepartureTime)
                .last("LIMIT 1");

        return baseMapper.selectOne(wrapper);
    }

    @Override
    public void markReturned(Long recordId) {
        if (recordId == null) {
            return;
        }

        ShipSailingRecordDO record = new ShipSailingRecordDO();
        record.setReturnTime(LocalDateTime.now());
        record.setId(recordId);
        record.setReturnStatus(SailingReturnStatusEnum.RETURNED.getCode());
        record.setGmtModified(LocalDateTime.now());

        baseMapper.updateById(record);
    }

    @Override
    public List<ShipSailingRecordDO> pageSailingRecordsWithWarning(SailingRecordQueryDTO queryDTO) {
        SailingRecordQueryCondition queryCondition = new SailingRecordQueryCondition();
        queryCondition.setShipName(queryDTO.getShipName());
        queryCondition.setPortName(queryDTO.getPortName());
        queryCondition.setDepartureStartDate(queryDTO.getDepartureStartDate());
        queryCondition.setDepartureEndDate(queryDTO.getDepartureEndDate());
        queryCondition.setReturnStatus(queryDTO.getReturnStatus());
        queryCondition.setWarningType(queryDTO.getWarningType());
        return baseMapper.pageSailingRecordsWithWarning(queryCondition);
    }

    @Override
    public Long countSailingRecords(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countSailingRecords(startTime, endTime);
    }

    @Override
    public Long countUniqueShips(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countUniqueShips(startTime, endTime);
    }

    @Override
    public List<DailySailingCountDTO> getDailySailingCounts(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getDailySailingCounts(startTime, endTime);
    }

    @Override
    public List<DailySailingCountDTO> getHourlySailingCounts(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getHourlySailingCounts(startTime, endTime);
    }

    @Override
    public List<PortAreaSailingRankDTO> getPortAreaSailingRanking(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getPortAreaSailingRanking(startTime, endTime);
    }

    @Override
    public Long countTodayShipSailingByType(LocalDateTime startTime, LocalDateTime endTime,  String portArea,  String shipType) {
        return baseMapper.countTodayShipSailingByType(startTime, endTime, portArea, shipType);
    }
}
