package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.ShipWarningDO;

/**
 * 船舶预警表 Repository 接口
 *
 * <AUTHOR> wong
 * @since 2025-08-18
 */
public interface ShipWarningRepository extends IService<ShipWarningDO> {

    /**
     * 根据出海记录ID和预警类型查询预警
     *
     * @param sailingRecordId 出海记录ID
     * @param warningType     预警类型
     * @return 预警信息
     */
    ShipWarningDO findByRecordIdAndType(Long sailingRecordId, String warningType);
}
