package com.deepinnet.sail.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.service.dto.*;
import com.deepinnet.sail.service.service.ShipSailingRecordService;
import com.deepinnet.sail.service.vo.SailingAnalysisVO;
import com.deepinnet.sail.service.vo.SailingRecordVO;
import com.deepinnet.sail.service.vo.TodayShipSailingVO;
import com.deepinnet.spatiotemporalplatform.dto.*;

import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 船舶出海记录管理控制器
 * </p>
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@RestController
@RequestMapping("/sail/sailing/record")
@Api(tags = "船舶出海记录管理")
public class ShipSailingRecordController {

    @Resource
    private ShipSailingRecordService shipSailingRecordService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询出海记录")
    public Result<CommonPage<SailingRecordVO>> pageSailingRecords(@RequestBody @Valid SailingRecordQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.pageSailingRecords(queryDTO));
    }

    @PostMapping("/trace")
    @ApiOperation(value = "查询出海记录轨迹")
    public Result<List<ShipSailingRecordTraceDTO>> getShipSailingRecordTrace(@RequestBody @Valid ShipSailingRecordTraceQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.getShipSailingRecordTrace(queryDTO));
    }

    @PostMapping("/analysis")
    @ApiOperation(value = "船舶出海分析", notes = "展示指定时间范围内的出海统计分析数据")
    public Result<SailingAnalysisVO> getSailingAnalysis(@RequestBody @Valid SailingAnalysisQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.getSailingAnalysis(queryDTO));
    }

    @PostMapping("/today")
    @ApiOperation(value = "今日船舶出海", notes = "展示今日船舶出海情况，包括统计数据和出海记录列表")
    public Result<TodayShipSailingVO> getTodayShipSailing(@RequestBody TodayShipSailingQueryDTO queryDTO) {
        return Result.success(shipSailingRecordService.getTodayShipSailing(queryDTO));
    }
}
