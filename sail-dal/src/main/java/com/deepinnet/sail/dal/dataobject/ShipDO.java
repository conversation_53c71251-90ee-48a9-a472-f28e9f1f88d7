package com.deepinnet.sail.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 船舶信息表
 * </p>
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Getter
@Setter
@TableName("ship")
public class ShipDO extends Model<ShipDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 船舶编号
     */
    @TableField("ship_no")
    private String shipNo;

    /**
     * 船舶名称
     */
    @TableField("ship_name")
    private String shipName;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 纳管公司
     */
    @TableField("management_company")
    private String managementCompany;

    /**
     * 所有人
     */
    @TableField("owner")
    private String owner;

    /**
     * 是否纳管
     */
    @TableField("is_managed")
    private Boolean isManaged;

    /**
     * 类型 (operation_management-运管, self_use-自用)
     */
    @TableField("ship_type")
    private String shipType;

    /**
     * 船主联系电话
     */
    @TableField("owner_phone")
    private String ownerPhone;

    /**
     * 自用船舶是否签署承诺书
     */
    @TableField("is_commitment_signed")
    private Boolean isCommitmentSigned;

    /**
     * 是否备案
     */
    @TableField("is_filed")
    private Boolean isFiled;

    /**
     * 包干责任人
     */
    @TableField("responsible_person")
    private String responsiblePerson;

    /**
     * 责任人联系电话
     */
    @TableField("responsible_person_phone")
    private String responsiblePersonPhone;

    /**
     * 港口编号
     */
    @TableField("port_code")
    private String portCode;

    /**
     * 所属港口
     */
    @TableField("port")
    private String port;

    /**
     * 所属办事处
     */
    @TableField("office")
    private String office;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    private Boolean isDeleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
