package com.deepinnet.sail.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;

import com.deepinnet.sail.dal.dataobject.*;
import com.deepinnet.sail.service.enums.*;
import com.deepinnet.sail.service.repository.*;
import com.deepinnet.sail.service.service.ShipTimeConfigService;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import org.locationtech.jts.geom.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.*;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 船舶预警定时任务
 *
 * <AUTHOR> wong
 * @since 2025-08-18
 */
@Component
public class ShipWarningTask {
    @Resource
    private ShipPositionRepository shipPositionRepository;

    @Resource
    private ShipWarningRepository shipWarningRepository;

    @Resource
    private ShipTimeConfigService shipTimeConfigService;

    @Resource
    private SeaAreaRepository seaAreaRepository;

    @Resource
    private ShipSailingRecordRepository sailingRecordRepository;

    private static final String RISK_TIME_SAILING = "risk_time_sailing";
    private static final String SEA_AREA_DEVIATION = "sea_area_deviation";
    private static final String FORBIDDEN_AREA = "forbidden_area";
    private static final String OVERDUE_RETURN = "overdue_return";
    private static final String DAILY_NOT_RETURN = "daily_not_return";
    private static final String REMOTE_NOT_RETURN = "remote_not_return";

    /**
     * 处理船舶位置数据，生成预警
     * 每5分钟执行一次
     */
    // @Scheduled(fixedRate = 5 * 60 * 1000)
    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.SECONDS)
    public void processShipPositions() {
        LogUtil.info("开始处理船舶位置数据，生成预警");

        // 获取最近10分钟内每个船舶的最新位置数据
        // 使用数据库窗口函数直接查询，避免内存中分组处理大量数据
        long tenMinutesAgo = System.currentTimeMillis() - 10 * 60 * 1000;

        List<ShipPositionDO> latestPositions = shipPositionRepository.getLatestPositionsByTimeRange(tenMinutesAgo);

        if (CollUtil.isEmpty(latestPositions)) {
            LogUtil.info("没有找到最近的船舶位置数据");
            return;
        }

        LogUtil.info("查询到{}个船舶的最新位置数据", latestPositions.size());

        // 处理每个船舶的最新位置数据
        int processedCount = 0;
        for (ShipPositionDO position : latestPositions) {
            try {
                processShipPosition(position);
                processedCount++;
            } catch (Exception e) {
                LogUtil.error("处理船舶{}位置数据失败", position.getShipNo(), e);
            }
        }

        LogUtil.info("处理船舶位置数据完成，共处理{}个船舶", processedCount);
    }

    /**
     * 处理单个船舶位置数据
     */
    private void processShipPosition(ShipPositionDO position) {
        if (position == null || StrUtil.isBlank(position.getShipNo())) {
            return;
        }

        String shipNo = position.getShipNo();
        String portCode = position.getPortCode();
        Point shipPoint = WktUtil.toPoint(position.getX(), position.getY());
        LocalDateTime positionTime = convertTimestampToLocalDateTime(position.getReportTime());

        // 检测是否离港
        boolean isDeparted = !isShipInPort(shipPoint, shipNo, portCode);

        ShipSailingRecordDO currentRecord = sailingRecordRepository.findCurrentSailingRecord(shipNo);

        if (isDeparted) {
            // 如果离港，检测有没有对应的【未归】状态的出海记录
            if (currentRecord == null) {
                // 如果没有出海记录则新增一条出海记录
                currentRecord = createSailingRecord(position, positionTime);
                LogUtil.info("船舶{}离港，创建出海记录，记录ID：{}", shipNo, currentRecord.getId());
            }

            // 检测对应预警的情况，是否需要生成预警
            checkAndCreateWarnings(currentRecord, position, positionTime, shipPoint);

        } else {
            // 如果未离港，查询有没有对应的【未归】状态的出海记录
            if (currentRecord != null) {
                // 如果有则把他的状态变为已归港
                sailingRecordRepository.markReturned(currentRecord.getId());
                LogUtil.info("船舶{}归港，更新出海记录状态，记录ID：{}", shipNo, currentRecord.getId());
            }
        }
    }

    /**
     * 创建出海记录
     */
    private ShipSailingRecordDO createSailingRecord(ShipPositionDO position, LocalDateTime departureTime) {
        ShipSailingRecordDO record = new ShipSailingRecordDO();
        record.setShipNo(position.getShipNo());
        record.setShipName(position.getShipName());
        record.setDepartureTime(departureTime);
        record.setReturnStatus(SailingReturnStatusEnum.SAILING.getCode());
        record.setGmtCreated(LocalDateTime.now());
        record.setGmtModified(LocalDateTime.now());
        record.setPortCode(position.getPortCode());
        record.setPortName(position.getPortName());
        sailingRecordRepository.save(record);
        return record;
    }

    /**
     * 检查并创建预警
     */
    private void checkAndCreateWarnings(ShipSailingRecordDO record, ShipPositionDO position,
                                        LocalDateTime positionTime, Point shipPoint) {
        // 检查6种预警类型，同一类型在同一出海记录中只创建一次
        checkAndCreateWarning(record, ShipWarningTypeEnum.RISK_TIME_SAILING.getCode(), position, positionTime, shipPoint);
        checkAndCreateWarning(record, ShipWarningTypeEnum.SEA_AREA_DEVIATION.getCode(), position, positionTime, shipPoint);
        checkAndCreateWarning(record, ShipWarningTypeEnum.FORBIDDEN_AREA.getCode(), position, positionTime, shipPoint);
        checkAndCreateWarning(record, ShipWarningTypeEnum.OVERDUE_RETURN.getCode(), position, positionTime, shipPoint);
        checkAndCreateWarning(record, ShipWarningTypeEnum.DAILY_NOT_RETURN.getCode(), position, positionTime, shipPoint);
        checkAndCreateWarning(record, ShipWarningTypeEnum.REMOTE_NOT_RETURN.getCode(), position, positionTime, shipPoint);
    }

    /**
     * 检查并创建单个预警
     */
    private void checkAndCreateWarning(ShipSailingRecordDO record, String warningType,
                                       ShipPositionDO position, LocalDateTime positionTime, Point shipPoint) {
        // 检查该出海记录是否已有此类型的预警
        ShipWarningDO existingWarning = shipWarningRepository.findByRecordIdAndType(record.getId(), warningType);
        // 已有此类型预警，不重复创建
        if (existingWarning != null) {
            return;
        }

        // 根据预警类型检查是否需要创建预警
        boolean shouldCreateWarning = false;
        String description = "";

        switch (warningType) {
            case RISK_TIME_SAILING:
                shouldCreateWarning = checkRiskTimeSailing(positionTime);
                description = "船舶在风险时段出海";
                break;

            case SEA_AREA_DEVIATION:
                shouldCreateWarning = checkSeaAreaDeviation(shipPoint);
                description = "船舶驶出休闲船舶规划海域范围";
                break;

            case FORBIDDEN_AREA:
                shouldCreateWarning = checkForbiddenArea(shipPoint);
                description = "船舶驶入禁海区域范围";
                break;

            case OVERDUE_RETURN:
                shouldCreateWarning = checkOverdueReturn(positionTime);
                description = "未在要求归港时间内归港";
                break;

            case DAILY_NOT_RETURN:
                shouldCreateWarning = checkDailyNotReturn(positionTime);
                description = "截止当日晚上24:00船舶未归港";
                break;

            case REMOTE_NOT_RETURN:
                shouldCreateWarning = checkRemoteNotReturn(record, positionTime, shipPoint);
                description = "船舶驶出休闲船舶规划海域范围，持续3天以上未归港";
                break;
        }

        if (shouldCreateWarning) {
            createWarning(record.getId(), position, warningType, description, positionTime);
        }
    }

    /**
     * 创建预警记录
     */
    private void createWarning(Long sailingRecordId, ShipPositionDO position, String warningType,
                               String description, LocalDateTime triggerTime) {
        ShipWarningDO warning = new ShipWarningDO();
        warning.setShipNo(position.getShipNo());
        warning.setShipName(position.getShipName());
        warning.setSailingRecordId(sailingRecordId);
        warning.setWarningType(warningType);
        warning.setDescription(description);
        warning.setX(position.getX());
        warning.setY(position.getY());
        warning.setTriggerTime(triggerTime);
        warning.setGmtCreated(LocalDateTime.now());
        warning.setGmtModified(LocalDateTime.now());

        shipWarningRepository.save(warning);

        LogUtil.info("创建船舶预警成功，船舶：{}，预警类型：{}，出海记录ID：{}",
                position.getShipNo(), warningType, sailingRecordId);
    }

    /**
     * 检查船舶是否在港口内
     */
    private boolean isShipInPort(Point shipPoint, String shipNo, String portCode) {
        try {
            // 查询港口类型的海域
            LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                    .eq(SeaAreaDO::getCode, portCode);

            SeaAreaDO portArea = seaAreaRepository.getOne(wrapper);
            if (portArea == null) {
                LogUtil.error("船舶关联的港口不存在，船舶编号：{}, portCode: {}", shipNo, portCode);
                throw new BizException(BizErrorCode.PORT_NOT_FOUND.getCode(), BizErrorCode.PORT_NOT_FOUND.getDesc());
            }

            if (StrUtil.isNotBlank(portArea.getWkt())) {
                Geometry portGeometry = WktUtil.toGeometry(portArea.getWkt());
                if (portGeometry != null && (portGeometry.contains(shipPoint) || portGeometry.covers(shipPoint))) {
                    return true;
                }
            }
        } catch (Exception e) {
            LogUtil.error("检查船舶{}是否在港口内失败", shipNo, e);
            throw e;
        }

        return false;
    }

    /**
     * 检查风险时段出海
     */
    private boolean checkRiskTimeSailing(LocalDateTime positionTime) {
        return shipTimeConfigService.isRegularRiskTime(positionTime) ||
                shipTimeConfigService.isCustomRiskTime(positionTime);
    }

    /**
     * 检查海域偏离
     */
    private boolean checkSeaAreaDeviation(Point shipPoint) {
        return !isShipInSeaAreaByType(shipPoint, SeaAreaTypeEnum.NORMAL.getCode());
    }

    /**
     * 检查禁海区
     */
    private boolean checkForbiddenArea(Point shipPoint) {
        return isShipInSeaAreaByType(shipPoint, SeaAreaTypeEnum.FORBIDDEN.getCode());
    }

    /**
     * 检查到时未归
     */
    private boolean checkOverdueReturn(LocalDateTime positionTime) {
        LocalTime maxReturnTime = shipTimeConfigService.getMaxReturnTime();
        if (maxReturnTime == null) {
            return false;
        }
        LocalTime currentTime = positionTime.toLocalTime();
        return currentTime.isAfter(maxReturnTime);
    }

    /**
     * 检查当日未归
     */
    private boolean checkDailyNotReturn(LocalDateTime positionTime) {
        LocalTime currentTime = positionTime.toLocalTime();
        return currentTime.isAfter(LocalTime.of(23, 59));
    }

    /**
     * 检查异地未归
     */
    private boolean checkRemoteNotReturn(ShipSailingRecordDO record, LocalDateTime positionTime, Point shipPoint) {
        // 检查是否在普通海域外
        boolean outOfNormalArea = !isShipInSeaAreaByType(shipPoint, SeaAreaTypeEnum.NORMAL.getCode());
        if (!outOfNormalArea) {
            return false;
        }

        // 检查是否持续3天以上
        LocalDateTime departureTime = record.getDepartureTime();
        long daysDiff = Duration.between(departureTime, positionTime).toDays();
        return daysDiff >= 3;
    }

    /**
     * 检查船舶是否在指定类型的海域内
     */
    private boolean isShipInSeaAreaByType(Point shipPoint, String seaAreaType) {
        try {
            LambdaQueryWrapper<SeaAreaDO> wrapper = Wrappers.lambdaQuery(SeaAreaDO.class)
                    .eq(SeaAreaDO::getType, seaAreaType);

            List<SeaAreaDO> seaAreas = seaAreaRepository.list(wrapper);

            for (SeaAreaDO seaArea : seaAreas) {
                if (StrUtil.isNotBlank(seaArea.getWkt())) {
                    Geometry seaAreaGeometry = WktUtil.toGeometry(seaArea.getWkt());
                    if (seaAreaGeometry != null && (seaAreaGeometry.contains(shipPoint) || (seaAreaGeometry.covers(shipPoint)))) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.error("检查船舶是否在海域内失败, seaAreaType: {}", seaAreaType, e);
            throw e;
        }

        return false;
    }

    /**
     * 时间戳转LocalDateTime
     */
    private LocalDateTime convertTimestampToLocalDateTime(Long timestamp) {
        if (timestamp == null) {
            return LocalDateTime.now();
        }
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }
}
