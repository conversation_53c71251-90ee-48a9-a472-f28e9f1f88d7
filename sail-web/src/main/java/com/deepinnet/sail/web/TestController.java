package com.deepinnet.sail.web;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.sail.service.dto.SailingAnalysisQueryDTO;
import com.deepinnet.sail.service.dto.TodayShipSailingQueryDTO;
import com.deepinnet.sail.service.service.ShipSailingRecordService;
import com.deepinnet.sail.service.vo.SailingAnalysisVO;
import com.deepinnet.sail.service.vo.TodayShipSailingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 测试控制器 - 用于验证GlobalControllerAdvise是否生效
 *
 * <AUTHOR> wong
 * @since 2025-08-20
 */
@RestController
@RequestMapping("/test")
@Api(tags = "测试接口")
public class TestController {

    @Resource
    private ShipSailingRecordService shipSailingRecordService;

    @GetMapping("/exception")
    @ApiOperation("测试异常处理")
    public Result<String> testException() {
        throw new BizException(BizErrorCode.ACCOUNT_NOT_LOGIN.getCode(), "测试异常处理");
    }

    @GetMapping("/illegal-argument")
    @ApiOperation("测试参数异常")
    public Result<String> testIllegalArgument() {
        throw new IllegalArgumentException("测试参数异常");
    }

    @GetMapping("/runtime-exception")
    @ApiOperation("测试运行时异常")
    public Result<String> testRuntimeException() {
        throw new RuntimeException("测试运行时异常");
    }

    @GetMapping("/success")
    @ApiOperation("测试正常返回")
    public Result<String> testSuccess() {
        return Result.success("测试成功");
    }

    @GetMapping("/sailing-analysis")
    @ApiOperation("测试船舶出海分析接口 - 本月")
    public Result<SailingAnalysisVO> testSailingAnalysis() {
        SailingAnalysisQueryDTO queryDTO = new SailingAnalysisQueryDTO();
        queryDTO.setTimeRangeType("this_month");
        
        return Result.success(shipSailingRecordService.getSailingAnalysis(queryDTO));
    }

    @GetMapping("/sailing-analysis/today")
    @ApiOperation("测试船舶出海分析接口 - 今日")
    public Result<SailingAnalysisVO> testSailingAnalysisToday() {
        SailingAnalysisQueryDTO queryDTO = new SailingAnalysisQueryDTO();
        queryDTO.setTimeRangeType("today");
        
        return Result.success(shipSailingRecordService.getSailingAnalysis(queryDTO));
    }

    @GetMapping("/sailing-analysis/yesterday")
    @ApiOperation("测试船舶出海分析接口 - 昨日")
    public Result<SailingAnalysisVO> testSailingAnalysisYesterday() {
        SailingAnalysisQueryDTO queryDTO = new SailingAnalysisQueryDTO();
        queryDTO.setTimeRangeType("yesterday");
        
        return Result.success(shipSailingRecordService.getSailingAnalysis(queryDTO));
    }

    @GetMapping("/sailing-analysis/last-month")
    @ApiOperation("测试船舶出海分析接口 - 上月")
    public Result<SailingAnalysisVO> testSailingAnalysisLastMonth() {
        SailingAnalysisQueryDTO queryDTO = new SailingAnalysisQueryDTO();
        queryDTO.setTimeRangeType("last_month");
        
        return Result.success(shipSailingRecordService.getSailingAnalysis(queryDTO));
    }

    @GetMapping("/sailing-analysis/custom")
    @ApiOperation("测试船舶出海分析接口 - 自定义时间")
    public Result<SailingAnalysisVO> testSailingAnalysisCustom() {
        SailingAnalysisQueryDTO queryDTO = new SailingAnalysisQueryDTO();
        queryDTO.setTimeRangeType("custom");
        queryDTO.setStartTime(LocalDateTime.of(2025, 8, 1, 0, 0, 0));
        queryDTO.setEndTime(LocalDateTime.of(2025, 8, 31, 23, 59, 59));
        
        return Result.success(shipSailingRecordService.getSailingAnalysis(queryDTO));
    }

    @GetMapping("/today-ship-sailing")
    @ApiOperation("测试今日船舶出海接口 - 全部片区")
    public Result<TodayShipSailingVO> testTodayShipSailing() {
        TodayShipSailingQueryDTO queryDTO = new TodayShipSailingQueryDTO();
        queryDTO.setPortCode(null);

        return Result.success(shipSailingRecordService.getTodayShipSailing(queryDTO));
    }

    @GetMapping("/today-ship-sailing/port-area")
    @ApiOperation("测试今日船舶出海接口 - 指定片区")
    public Result<TodayShipSailingVO> testTodayShipSailingByPortArea() {
        TodayShipSailingQueryDTO queryDTO = new TodayShipSailingQueryDTO();
        queryDTO.setPortCode("东港片区");

        return Result.success(shipSailingRecordService.getTodayShipSailing(queryDTO));
    }
}
