package com.deepinnet.sail.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.sail.dal.condition.SailingRecordQueryCondition;
import com.deepinnet.sail.dal.dataobject.ShipSailingRecordDO;
import com.deepinnet.sail.dal.dto.DailySailingCountDTO;
import com.deepinnet.sail.dal.dto.PortAreaSailingRankDTO;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 船舶出海记录表 Mapper 接口
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
@Mapper
public interface ShipSailingRecordMapper extends BaseMapper<ShipSailingRecordDO> {

    /**
     * 分页查询出海记录（支持预警类型过滤）
     *
     * @param queryCondition 查询条件
     * @return 出海记录列表
     */
    List<ShipSailingRecordDO> pageSailingRecordsWithWarning(@Param("query") SailingRecordQueryCondition queryCondition);

    /**
     * 统计指定时间范围内的出海总次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 出海总次数
     */
    Long countSailingRecords(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的出海船舶数（去重）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 出海船舶数
     */
    Long countUniqueShips(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内每日出海次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每日出海次数
     */
    List<DailySailingCountDTO> getDailySailingCounts(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内每小时出海次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每小时出海次数
     */
    List<DailySailingCountDTO> getHourlySailingCounts(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内各港口片区出海次数排行
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 港口片区出海次数排行
     */
    List<PortAreaSailingRankDTO> getPortAreaSailingRanking(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计今日指定类型船舶出海次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param portArea 港口片区（可为空）
     * @param shipType 船舶类型
     * @return 指定类型船舶出海次数
     */
    Long countTodayShipSailingByType(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("portArea") String portArea, @Param("shipType") String shipType);
}
