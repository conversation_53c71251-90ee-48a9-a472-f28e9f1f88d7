package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.ShipSailingRecordDO;
import com.deepinnet.sail.dal.dto.DailySailingCountDTO;
import com.deepinnet.sail.dal.dto.PortAreaSailingRankDTO;
import com.deepinnet.sail.service.dto.SailingRecordQueryDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 船舶出海记录表 Repository 接口
 *
 * <AUTHOR> wong
 * @since 2025-08-19
 */
public interface ShipSailingRecordRepository extends IService<ShipSailingRecordDO> {

    /**
     * 查询船舶当前未归港的出海记录
     *
     * @param shipNo 船舶编号
     * @return 出海记录
     */
    ShipSailingRecordDO findCurrentSailingRecord(String shipNo);

    /**
     * 标记船舶归港
     *
     * @param recordId 出海记录ID
     */
    void markReturned(Long recordId);

    /**
     * 管理后台分页查询出海记录
     *
     * @param queryDTO
     * @return
     */
    List<ShipSailingRecordDO> pageSailingRecordsWithWarning(SailingRecordQueryDTO queryDTO);

    /**
     * 统计指定时间范围内的出海总次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 出海总次数
     */
    Long countSailingRecords(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内的出海船舶数（去重）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 出海船舶数
     */
    Long countUniqueShips(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内每日出海次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 每日出海次数
     */
    List<DailySailingCountDTO> getDailySailingCounts(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内每小时出海次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 每小时出海次数
     */
    List<DailySailingCountDTO> getHourlySailingCounts(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内各港口片区出海次数排行
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 港口片区出海次数排行
     */
    List<PortAreaSailingRankDTO> getPortAreaSailingRanking(LocalDateTime startTime, LocalDateTime endTime);

    Long countTodayShipSailingByType(LocalDateTime startTime, LocalDateTime endTime, String portArea, String shipType);
}
