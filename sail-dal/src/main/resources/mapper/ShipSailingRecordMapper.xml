<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.ShipSailingRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.ShipSailingRecordDO">
        <id column="id" property="id"/>
        <result column="ship_no" property="shipNo"/>
        <result column="ship_name" property="shipName"/>
        <result column="port_code" property="portCode"/>
        <result column="port_name" property="portName"/>
        <result column="departure_time" property="departureTime"/>
        <result column="return_time" property="returnTime"/>
        <result column="return_status" property="returnStatus"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ship_no, ship_name, port_code, port_name, departure_time, return_time, return_status, tenant_id, gmt_created, gmt_modified
    </sql>

    <!-- 分页查询出海记录（支持预警类型过滤） -->
    <select id="pageSailingRecordsWithWarning" resultMap="BaseResultMap">
        SELECT DISTINCT ssr.*
        FROM ship_sailing_record ssr
        <if test="query.warningType != null and query.warningType != ''">
            INNER JOIN ship_warning sw ON ssr.id = sw.sailing_record_id
            AND sw.warning_type = #{query.warningType}
        </if>
        <where>
            <if test="query.shipName != null and query.shipName != ''">
                AND ssr.ship_name LIKE CONCAT('%', #{query.shipName}, '%')
            </if>
            <if test="query.portName != null and query.portName != ''">
                AND ssr.port_name = #{query.portName}
            </if>
            <if test="query.departureStartDate != null">
                AND ssr.departure_time >= #{query.departureStartDate}
            </if>
            <if test="query.departureEndDate != null">
                AND ssr.departure_time &lt;= #{query.departureEndDate}
            </if>
            <if test="query.returnStatus != null and query.returnStatus != ''">
                AND ssr.return_status = #{query.returnStatus}
            </if>
        </where>
        ORDER BY ssr.departure_time DESC
    </select>

    <!-- 统计指定时间范围内的出海总次数 -->
    <select id="countSailingRecords" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          AND is_deleted = false
    </select>

    <!-- 统计指定时间范围内的出海船舶数（去重） -->
    <select id="countUniqueShips" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ship_no)
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          AND is_deleted = false
    </select>

    <!-- 每日出海次数统计结果映射 -->
    <resultMap id="DailySailingCountResultMap" type="com.deepinnet.sail.dal.dto.DailySailingCountDTO">
        <result column="date" property="date"/>
        <result column="count" property="count"/>
    </resultMap>

    <!-- 港口片区出海次数排行结果映射 -->
    <resultMap id="PortAreaSailingRankResultMap" type="com.deepinnet.sail.dal.dto.PortAreaSailingRankDTO">
        <result column="portArea" property="portArea"/>
        <result column="count" property="count"/>
    </resultMap>

    <!-- 统计指定时间范围内每日出海次数 -->
    <select id="getDailySailingCounts" resultMap="DailySailingCountResultMap">
        SELECT
            TO_CHAR(departure_time, 'MM/DD') as date,
            COUNT(*) as count
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          AND is_deleted = false
        GROUP BY DATE(departure_time), TO_CHAR(departure_time, 'MM/DD')
        ORDER BY DATE(departure_time)
    </select>

    <!-- 统计指定时间范围内每小时出海次数 -->
    <select id="getHourlySailingCounts" resultMap="DailySailingCountResultMap">
        SELECT
            TO_CHAR(departure_time, 'HH24:00') as date,
            COUNT(*) as count
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          AND is_deleted = false
        GROUP BY EXTRACT(HOUR FROM departure_time), TO_CHAR(departure_time, 'HH24:00')
        ORDER BY EXTRACT(HOUR FROM departure_time)
    </select>

    <!-- 统计指定时间范围内各港口片区出海次数排行 -->
    <select id="getPortAreaSailingRanking" resultMap="PortAreaSailingRankResultMap">
        SELECT
            port_name as portArea,
            COUNT(*) as count
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          AND is_deleted = false
          AND port_name IS NOT NULL
          AND port_name != ''
        GROUP BY port_name
        ORDER BY COUNT(*) DESC
    </select>

    <!-- 统计今日指定类型船舶出海次数 -->
    <select id="countTodayShipSailingByType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM ship_sailing_record ssr
        LEFT JOIN ship s ON ssr.ship_no = s.ship_no
        WHERE ssr.departure_time >= #{startTime}
          AND ssr.departure_time &lt;= #{endTime}
          AND s.ship_type = #{shipType}
          <if test="portArea != null and portArea != ''">
              AND ssr.port_name = #{portArea}
          </if>
    </select>

    <!-- 统计今日出海船舶数（去重） -->
    <select id="countDistinctTodayShips" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ship_no)
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          <if test="portArea != null and portArea != ''">
              AND port_name = #{portArea}
          </if>
    </select>

    <!-- 统计今日已归港船舶数（去重） -->
    <select id="countDistinctReturnedShips" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ship_no)
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          AND return_status = 'RETURNED'
          <if test="portArea != null and portArea != ''">
              AND port_name = #{portArea}
          </if>
    </select>

    <!-- 统计今日未归港船舶数（去重） -->
    <select id="countDistinctNotReturnedShips" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT ship_no)
        FROM ship_sailing_record
        WHERE departure_time >= #{startTime}
          AND departure_time &lt;= #{endTime}
          AND return_status = 'SAILING'
          <if test="portArea != null and portArea != ''">
              AND port_name = #{portArea}
          </if>
    </select>

</mapper>
