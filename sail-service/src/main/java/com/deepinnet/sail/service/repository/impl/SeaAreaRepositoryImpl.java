package com.deepinnet.sail.service.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.sail.dal.dataobject.SeaAreaDO;
import com.deepinnet.sail.dal.mapper.SeaAreaMapper;
import com.deepinnet.sail.service.repository.SeaAreaRepository;
import org.springframework.stereotype.Repository;

/**
 * 海域表 Repository 实现类
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
@Repository
public class SeaAreaRepositoryImpl extends ServiceImpl<SeaAreaMapper, SeaAreaDO> implements SeaAreaRepository {

}
