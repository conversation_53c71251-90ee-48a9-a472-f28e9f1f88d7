package com.deepinnet.sail.service.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.sail.dal.dataobject.ShipPositionDO;

import java.util.List;

/**
 * <p>
 * 船舶位置信息表 服务类
 * </p>
 *
 * <AUTHOR> Agent
 * @since 2025-08-15
 */
public interface ShipPositionRepository extends IService<ShipPositionDO> {

    /**
     * 获取指定时间范围内每个船舶的最新位置数据
     *
     * @param startTime 开始时间戳（毫秒）
     * @return 每个船舶的最新位置数据列表
     */
    List<ShipPositionDO> getLatestPositionsByTimeRange(Long startTime);
}
