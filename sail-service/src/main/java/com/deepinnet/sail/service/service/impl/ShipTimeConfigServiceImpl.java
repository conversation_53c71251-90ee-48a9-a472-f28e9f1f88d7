package com.deepinnet.sail.service.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.*;
import com.deepinnet.sail.common.error.BizErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;

import com.deepinnet.sail.dal.dataobject.ShipTimeConfigDO;
import com.deepinnet.sail.service.enums.ShipTimeConfigTypeEnum;
import com.deepinnet.sail.service.repository.ShipTimeConfigRepository;
import com.deepinnet.sail.service.service.ShipTimeConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;

/**
 * 船舶时间配置服务
 *
 * <AUTHOR> wong
 * @since 2025-08-15
 */
@Service
public class ShipTimeConfigServiceImpl implements ShipTimeConfigService {

    @Resource
    private ShipTimeConfigRepository configRepository;

    /**
     * 是否是常规风险时段
     *
     * @param checkTime 检查时间
     * @return true-是风险时段，false-不是风险时段
     */
    public boolean isRegularRiskTime(LocalDateTime checkTime) {
        String configValue = getConfigValue(ShipTimeConfigTypeEnum.REGULAR_RISK_TIME.getCode());
        if (StrUtil.isBlank(configValue)) {
            return false;
        }

        try {
            JSONArray config = JSONUtil.parseArray(configValue);

            LocalTime currentTime = checkTime.toLocalTime();

            for (int i = 0; i < config.size(); i++) {
                JSONObject range = config.getJSONObject(i);
                LocalTime startTime = LocalTime.parse(range.getStr("startTime"));
                LocalTime endTime = LocalTime.parse(range.getStr("endTime"));

                if (isTimeInRange(currentTime, startTime, endTime)) {
                    return true;
                }
            }
        } catch (Exception e) {
            LogUtil.error("解析常规风险时段配置失败, configValue: {}", configValue, e);
            throw new BizException(BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getCode(), BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getDesc());
        }

        return false;
    }

    /**
     * 是否是自定义风险时段
     *
     * @param checkTime 检查时间
     * @return true-是风险时段，false-不是风险时段
     */
    public boolean isCustomRiskTime(LocalDateTime checkTime) {
        String configValue = getConfigValue(ShipTimeConfigTypeEnum.CUSTOM_RISK_TIME.getCode());
        if (StrUtil.isBlank(configValue)) {
            return false;
        }

        try {
            JSONArray dateTimeRanges = JSONUtil.parseArray(configValue);

            for (int i = 0; i < dateTimeRanges.size(); i++) {
                JSONObject range = dateTimeRanges.getJSONObject(i);
                LocalDateTime startDateTime = LocalDateTime.parse(range.getStr("startDateTime"),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
                LocalDateTime endDateTime = LocalDateTime.parse(range.getStr("endDateTime"),
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

                if ((checkTime.isAfter(startDateTime) || checkTime.isEqual(startDateTime)) &&
                        (checkTime.isBefore(endDateTime) || checkTime.isEqual(endDateTime))) {
                    return true;
                }
            }
        } catch (Exception e) {
            LogUtil.error("解析自定义风险时段配置失败, configValue: {}", configValue, e);
            throw new BizException(BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getCode(), BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getDesc());
        }

        return false;
    }

    /**
     * 是否按时归港（是否超过最迟归港时间）
     *
     * @param returnTime 归港时间
     * @return true-按时归港，false-超过最迟归港时间
     */
    public boolean isOnTimeReturn(LocalDateTime returnTime) {
        String configValue = getConfigValue(ShipTimeConfigTypeEnum.DAILY_RETURN_TIME.getCode());
        if (StrUtil.isBlank(configValue)) {
            // 没有配置则认为按时
            return true;
        }

        try {
            JSONObject config = JSONUtil.parseObj(configValue);
            String returnTimeStr = config.getStr("returnTime");
            LocalTime maxReturnTime = LocalTime.parse(returnTimeStr);
            LocalTime actualReturnTime = returnTime.toLocalTime();

            return actualReturnTime.isBefore(maxReturnTime) || actualReturnTime.equals(maxReturnTime);
        } catch (Exception e) {
            LogUtil.error("解析最迟归港时间配置失败, configValue: {}", configValue, e);
            throw new BizException(BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getCode(), BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getDesc());
        }
    }

    /**
     * 获取最迟归港时间
     *
     * @return 最迟归港时间
     */
    public LocalTime getMaxReturnTime() {
        String configValue = getConfigValue(ShipTimeConfigTypeEnum.DAILY_RETURN_TIME.getCode());
        if (StrUtil.isBlank(configValue)) {
            return null;
        }

        try {
            JSONObject config = JSONUtil.parseObj(configValue);
            String returnTimeStr = config.getStr("maxReturnTime");
            return LocalTime.parse(returnTimeStr);
        } catch (Exception e) {
            LogUtil.error("解析最迟归港时间配置失败, configValue: {}", configValue, e);
            throw new BizException(BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getCode(), BizErrorCode.PARSE_SHIP_TIME_CONFIG_ERROR.getDesc());
        }
    }

    /**
     * 保存配置
     *
     * @param configType  配置类型
     * @param configValue 配置值
     */
    public void saveConfig(String configType, String configValue) {
        ShipTimeConfigDO config = configRepository.findByTenantIdAndConfigType(configType);

        if (config == null) {
            config = new ShipTimeConfigDO();
            config.setConfigType(configType);
            config.setIsEnabled(true);
            config.setGmtCreated(LocalDateTime.now());
        }

        config.setConfigValue(configValue);
        config.setGmtModified(LocalDateTime.now());

        configRepository.saveOrUpdate(config);
    }

    /**
     * 获取配置值
     *
     * @param configType 配置类型
     * @return 配置值
     */
    private String getConfigValue(String configType) {
        ShipTimeConfigDO config = configRepository.findByTenantIdAndConfigType(configType);
        return config != null && config.getIsEnabled() ? config.getConfigValue() : null;
    }

    /**
     * 判断时间是否在范围内（处理跨天情况）
     *
     * @param checkTime 检查时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return true-在范围内，false-不在范围内
     */
    private boolean isTimeInRange(LocalTime checkTime, LocalTime startTime, LocalTime endTime) {
        if (startTime.isBefore(endTime)) {
            // 不跨天：如 08:00 - 18:00
            return !checkTime.isBefore(startTime) && !checkTime.isAfter(endTime);
        } else {
            // 跨天：如 20:00 - 06:00
            return !checkTime.isBefore(startTime) || !checkTime.isAfter(endTime);
        }
    }
}
