<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.ShipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.ShipDO">
        <id column="id" property="id"/>
        <result column="ship_no" property="shipNo"/>
        <result column="ship_name" property="shipName"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="management_company" property="managementCompany"/>
        <result column="owner" property="owner"/>
        <result column="is_managed" property="isManaged"/>
        <result column="ship_type" property="shipType"/>
        <result column="owner_phone" property="ownerPhone"/>
        <result column="is_commitment_signed" property="isCommitmentSigned"/>
        <result column="is_filed" property="isFiled"/>
        <result column="responsible_person" property="responsiblePerson"/>
        <result column="responsible_person_phone" property="responsiblePersonPhone"/>
        <result column="port_code" property="portCode"/>
        <result column="port" property="port"/>
        <result column="office" property="office"/>
        <result column="remarks" property="remarks"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ship_no, ship_name, gmt_created, gmt_modified, management_company, owner,
        is_managed, ship_type, owner_phone, is_commitment_signed, is_filed,
        responsible_person, responsible_person_phone, port_code, port, office, remarks
    </sql>

</mapper>
