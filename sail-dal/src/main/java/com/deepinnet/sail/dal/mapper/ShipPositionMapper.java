package com.deepinnet.sail.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.sail.dal.dataobject.ShipPositionDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * 船舶位置信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> wong
 * @since 2025-08-15
 */
@Mapper
public interface ShipPositionMapper extends BaseMapper<ShipPositionDO> {

    /**
     * 获取指定时间范围内每个船舶的最新位置数据
     * 使用窗口函数ROW_NUMBER()来获取每个船舶的最新记录，避免内存中分组
     *
     * @param startTime 开始时间戳（毫秒）
     * @return 每个船舶的最新位置数据列表
     */
    List<ShipPositionDO> getLatestPositionsByTimeRange(@Param("startTime") Long startTime);
}
