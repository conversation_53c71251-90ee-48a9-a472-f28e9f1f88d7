package com.deepinnet.sail.common.error;

import lombok.Getter;

/**
 * 业务错误码
 *
 * <AUTHOR>
 */
@Getter
public enum BizErrorCode {

    ACCOUNT_LOGIN_EXPIRE("account_login_expire", "当前账号登录已经失效，请重新登录"),

    ACCOUNT_NOT_LOGIN("account_not_login", "当前账号未登录，请先登录"),

    FILE_SIZE_EXCEEDS_THE_LIMIT("file_size_exceeds_the_limit", "文件大小超过限制"),

    /**
     * 船舶
     */
    PARSE_SHIP_TIME_CONFIG_ERROR("parse_ship_time_config_error", "解析船舶时段配置失败"),

    PORT_NOT_FOUND("port_not_found", "港口不存在"),

    SAILING_RECORD_NOT_FOUND("sailing_record_not_found", "出海记录不存在"),

    ;

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String desc;

    BizErrorCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}