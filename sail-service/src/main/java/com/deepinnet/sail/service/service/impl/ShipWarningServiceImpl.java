package com.deepinnet.sail.service.service.impl;

import com.deepinnet.sail.service.repository.ShipWarningRepository;
import com.deepinnet.sail.service.service.ShipWarningService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 船舶预警服务实现类
 *
 * <AUTHOR> wong
 * @since 2025-08-18
 */
@Service
public class ShipWarningServiceImpl implements ShipWarningService {

    @Resource
    private ShipWarningRepository shipWarningRepository;

}
