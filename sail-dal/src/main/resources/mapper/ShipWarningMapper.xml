<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.sail.dal.mapper.ShipWarningMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.sail.dal.dataobject.ShipWarningDO">
        <id column="id" property="id"/>
        <result column="ship_no" property="shipNo"/>
        <result column="ship_name" property="shipName"/>
        <result column="warning_type" property="warningType"/>
        <result column="description" property="description"/>
        <result column="x" property="x"/>
        <result column="y" property="y"/>
        <result column="trigger_time" property="triggerTime"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ship_no, ship_name, warning_type, status, description, x, y, 
        trigger_time, close_time, close_reason, ext_info, gmt_created, gmt_modified
    </sql>

</mapper>
